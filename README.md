This code is for apache-supeset 4.0.2
# Apache Superset Local Setup Instructions
## Prerequestie for Running in Local Debugging mode:
   1. Python version required: 3.11.9
   2. Node version required: v18.17.1
   3. Mysql should be installed: "brew install mysql"
   4. Redis should be installed: "brew install redis"
   5. Start local redis: "brew services start redis" (Make sure redis is started in system).

## To Run in Local in Debugging mode:
   1. Create virual env using this command -> "python3 -m venv venv".
   2. Activate virual env using this command -> "source venv/bin/activate".
   3. Install dependencies using below commands:
      - pip install -r requirements/base.txt
      - pip install -r requirements/development.txt
      - pip install -r requirements/local.txt
      - pip install -e .
   4. Open new terminal and go to crux-sidecar folder and run below command:
      - "docker-compose up" command to fetch token which data-explorer uses for login.
   4. Run using Vscode config present in .vscode folder

## To Run using Docker in Local Without Debugging:
   1. Make sure Build image in Local using this command -> "docker build -t superset-local-node ."
   2. Make Sure put your below db creds in .localenv file 
      - DATABASE_PASSWORD=${PASS}
      - DATABASE_USER={$USER}
      - DATABASE_DB=${DB}
      - DATABASE_PORT=${PORT}
   3. Then Run "docker-compose up" command.

Prometheus Metrics available at this endpoint: http://localhost:8088/metrics