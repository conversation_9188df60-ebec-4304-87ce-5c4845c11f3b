#!/usr/bin/env bash

# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# ------------------------------------------------------------------------
# Creates multiple tenant databases and respective users for multi-tenant
# Superset deployment. Each tenant gets its own database and user.
# ------------------------------------------------------------------------
set -e

echo "Initializing multi-tenant databases..."

# Function to create a tenant database and user
create_tenant_db() {
    local tenant_name=$1
    local db_name="${tenant_name}_superset"
    local user_name="${tenant_name}_user"
    local password="${tenant_name}_password"
    
    echo "Creating database and user for tenant: ${tenant_name}"
    
    # Create user and database
    psql -v ON_ERROR_STOP=1 --username "${POSTGRES_USER}" <<-EOSQL
        CREATE USER ${user_name} WITH PASSWORD '${password}';
        CREATE DATABASE ${db_name};
        GRANT ALL PRIVILEGES ON DATABASE ${db_name} TO ${user_name};
EOSQL

    # Grant schema permissions
    psql -v ON_ERROR_STOP=1 --username "${POSTGRES_USER}" -d "${db_name}" <<-EOSQL
        GRANT ALL ON SCHEMA public TO ${user_name};
EOSQL

    echo "Successfully created tenant database: ${db_name} with user: ${user_name}"
}

# Create tenant databases based on POSTGRES_MULTIPLE_DATABASES environment variable
if [ -n "${POSTGRES_MULTIPLE_DATABASES}" ]; then
    echo "POSTGRES_MULTIPLE_DATABASES: ${POSTGRES_MULTIPLE_DATABASES}"
    
    # Split the comma-separated list and create databases
    IFS=',' read -ra DATABASES <<< "${POSTGRES_MULTIPLE_DATABASES}"
    for db in "${DATABASES[@]}"; do
        # Skip the main superset database as it's already created
        if [ "${db}" != "superset" ]; then
            # Extract tenant name from database name (e.g., tenant1_superset -> tenant1)
            if [[ "${db}" == *"_superset" ]]; then
                tenant_name="${db%_superset}"
                create_tenant_db "${tenant_name}"
            else
                echo "Skipping database ${db} - doesn't match tenant naming pattern"
            fi
        fi
    done
else
    echo "No POSTGRES_MULTIPLE_DATABASES specified, skipping multi-tenant database creation"
fi

echo "Multi-tenant database initialization completed"
