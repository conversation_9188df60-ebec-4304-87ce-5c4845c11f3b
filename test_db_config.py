#!/usr/bin/env python3
"""
Test script to verify database configuration for different tenants.
"""

import sys
import os
sys.path.insert(0, 'superset')

def test_localenv_loading():
    """Test loading configuration from .localenv"""
    print("Testing .localenv loading...")
    
    from superset.utils.tenant import TenantManager
    
    tm = TenantManager()
    
    print("Loaded .localenv config:")
    for key, value in tm._localenv_config.items():
        if 'PASSWORD' in key:
            print(f"  {key}=***")  # Hide password
        else:
            print(f"  {key}={value}")

def test_tenant_db_configs():
    """Test database configuration generation for tenants"""
    print("\nTesting tenant database configurations...")
    
    from superset.utils.tenant import TenantManager
    from flask import Flask
    
    # Create test app with config
    app = Flask(__name__)
    app.config['ALLOWED_TENANTS'] = ['tenant1', 'tenant2']
    app.config['MULTI_TENANT_ENABLED'] = True
    
    tm = TenantManager()
    
    with app.app_context():
        for tenant in ['tenant1', 'tenant2']:
            print(f"\nTenant: {tenant}")
            print("-" * 40)
            
            try:
                if tm.is_valid_tenant(tenant):
                    config = tm.get_tenant_database_config(tenant)
                    uri = tm.get_tenant_database_uri(tenant)
                    
                    print(f"✅ Valid tenant: {tenant}")
                    print(f"Database: {config['database']}")
                    print(f"Host: {config['host']}")
                    print(f"Port: {config['port']}")
                    print(f"User: {config['username']}")
                    print(f"URI: {uri}")
                else:
                    print(f"❌ Invalid tenant: {tenant}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")

def test_db_connection_mutator():
    """Test the database connection mutator function"""
    print("\nTesting DB Connection Mutator...")
    
    # Import the mutator function
    sys.path.insert(0, 'superset')
    from superset.superset_config import multi_tenant_db_connection_mutator
    from sqlalchemy.engine.url import make_url
    from flask import Flask
    
    # Create test app
    app = Flask(__name__)
    app.config['MULTI_TENANT_ENABLED'] = True
    app.config['ALLOWED_TENANTS'] = ['tenant1', 'tenant2']
    
    with app.app_context():
        # Test original URI
        original_uri = make_url("postgresql://user:pass@localhost:5432/postgres")
        
        print(f"Original URI: {original_uri}")
        
        # Test scenarios
        scenarios = [
            {"tenant": "tenant1", "desc": "With tenant1 context"},
            {"tenant": "tenant2", "desc": "With tenant2 context"},
            {"tenant": None, "desc": "Without tenant context"},
        ]
        
        for scenario in scenarios:
            print(f"\nScenario: {scenario['desc']}")
            
            # Mock tenant context
            if scenario['tenant']:
                from flask import g
                g.tenant_id = scenario['tenant']
            
            try:
                new_uri, params = multi_tenant_db_connection_mutator(
                    original_uri, {}, "test_user", None, None
                )
                print(f"Result URI: {new_uri}")
            except Exception as e:
                print(f"Error: {e}")
            
            # Clear tenant context
            if hasattr(g, 'tenant_id'):
                delattr(g, 'tenant_id')

if __name__ == "__main__":
    print("Database Configuration Test")
    print("=" * 50)
    
    try:
        test_localenv_loading()
        test_tenant_db_configs()
        test_db_connection_mutator()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
