#!/bin/bash

export FLASK_APP="superset/app.py"
export SUPERSET_CONFIG_PATH="/Users/<USER>/Downloads/test/superset-test/superset/superset_config.py"
export SUPERSET_HOME="/tmp/superset_home"
export DATABASE_DB="supersetDB"
export DATABASE_HOST="localhost"
export DATABASE_PASSWORD="postgres"
export DATABASE_USER="postgres"
export DATABASE_DIALECT="postgresql"
export DATABASE_PORT="5432"
export MAPBOX_API_KEY=""
export TENANT_ID="0"
export SANDBOX_ID="1"
export REALM="0-admin.India"
export CLIENT_ID="eMsKSpykyaISGlkQngcY.localt"
export CLIENT_SECRET="qD2OdvFETYPFL_HKeW7-0_jYtH-xKLsmbHfBU-85"
export TOKEN_URL="https://sso-pp.zetaapps.in/"
export USER_INFO_URL="https://sso-pp.zetaapps.in/userInfo"
export API_URL="https://sso-pp.zetaapps.in/"
export AUTHORIZE_URL="https://0-0-cipher.mum1-pp.zetaapps.in/sso/authorize"
export PROTEUS_URL="https://proteus-cipher.mum1-pp.zeta.in/"
export SCOPE="admin"
export CRUX_URL="http://localhost:7000"
export SECRET_KEY="x1IEZMjzmEqH0k0zfP/YQG5RsQFbQJ7c7uRyBNAkk9fR66Lpkz7WaegA"
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export WEBDRIVER_BASEURL="http://localhost:8088"
export WEBDRIVER_BASEURL_USER_FRIENDLY="http://localhost:8088"
export SMTP_HOST="smtp.gmail.com"
export SMTP_PORT="587"
export SMTP_USER=""
export SMTP_PASSWORD=""
export SMTP_MAIL_FROM=""
export SUPERSET_SECRET_KEY="X5sQ3v9yH@McQfTjWnZr4u7w!z%C*F-JaNdRgUkXp2s5v8"
export PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_multiproc_dir
mkdir -p /tmp/prometheus_multiproc_dir
superset db upgrade && superset init
gunicorn -w 4 -k gthread -b 0.0.0.0:8088 "superset.app:create_app()"