# Multi-Tenant Superset Setup

This document describes the multi-tenant architecture implemented for Apache Superset, allowing multiple tenants to use separate databases while sharing the same application instance.

## Architecture Overview

The multi-tenant implementation uses the following approach:

- **Tenant Identification**: Via route parameters (`/tenant/{tenant_id}/...`)
- **Database Strategy**: Separate database per tenant
- **Database Naming**: `{tenant_id}_postgres` (e.g., `tenant1_postgres`, `tenant2_postgres`)
- **Credentials**: Shared credentials from `.localenv` file
- **Runtime Switching**: Database connections are switched at runtime based on tenant context

## Components

### 1. Tenant Manager (`superset/utils/tenant.py`)

- Manages tenant context and database configurations
- Validates tenant requests against allowed tenant list
- Loads database configuration from `.localenv` file
- Caches tenant configurations for performance

### 2. Tenant Middleware (`superset/middleware/tenant.py`)

- Intercepts tenant routes (`/tenant/{tenant_id}/...`) to extract and validate tenant information
- Stores tenant context in Flask's `g` object
- Handles tenant-related errors gracefully
- Skips validation for non-tenant routes and static assets

### 3. Tenant Routes (`superset/views/tenant.py`)

- Provides tenant-aware URL routing
- Handles routes like `/tenant/{tenant_id}/dashboard/`, `/tenant/{tenant_id}/explore/`
- Redirects to appropriate Superset views with tenant context

### 3. Database Connection Mutator

- Implemented in `superset/superset_config.py`
- Uses Superset's built-in `DB_CONNECTION_MUTATOR` mechanism
- Switches database connections at runtime based on tenant context
- Logs database switches for debugging

### 4. Multi-Database Docker Setup

- Modified `docker-compose-dev.yml` to support multiple databases
- Database initialization script creates tenant databases automatically
- Environment variables configure tenant credentials

## Configuration

### Environment Variables

Add these to your `docker/.env` file:

```bash
# Multi-tenant configuration
MULTI_TENANT_ENABLED=true
ALLOWED_TENANTS=tenant1,tenant2

# Tenant database credentials (in production, use vault)
TENANT1_DB_USER=tenant1_user
TENANT1_DB_PASSWORD=tenant1_password
TENANT2_DB_USER=tenant2_user
TENANT2_DB_PASSWORD=tenant2_password
```

### Docker Compose

The `docker-compose-dev.yml` has been updated to create multiple databases:

```yaml
environment:
  POSTGRES_MULTIPLE_DATABASES: "superset,tenant1_superset,tenant2_superset"
```

## Usage

### Making Tenant-Aware Requests

All tenant-specific requests must use the tenant route format:

```bash
# Example tenant URLs
curl http://localhost:8088/tenant/tenant1/dashboard/
curl http://localhost:8088/tenant/tenant2/explore/
curl http://localhost:8088/tenant/tenant1/sqllab/
```

### Available Tenant Routes

- `/tenant/{tenant_id}/` - Tenant home (redirects to dashboard)
- `/tenant/{tenant_id}/dashboard/` - Tenant dashboard
- `/tenant/{tenant_id}/explore/` - Tenant explore view
- `/tenant/{tenant_id}/sqllab/` - Tenant SQL Lab

### Frontend Integration

When building tenant-aware frontend applications, use tenant-specific URLs:

```javascript
// Example: Navigate to tenant dashboard
window.location.href = `/tenant/${tenantId}/dashboard/`;

// Example: API calls will automatically use tenant context
fetch("/api/v1/dashboard/", {
  headers: {
    "Content-Type": "application/json",
  },
});
```

## Database Schema

Each tenant must have a pre-existing database with the following naming convention:

- Main Superset database: `superset`
- Tenant 1 database: `tenant1_superset`
- Tenant 2 database: `tenant2_superset`

**Important**: Tenant databases are **not created automatically**. They must exist before starting Superset.

Each tenant database contains the complete Superset schema and is isolated from other tenants.

## Security Considerations

1. **Tenant Validation**: Only pre-configured tenants in `ALLOWED_TENANTS` are allowed
2. **Database Isolation**: Each tenant has completely separate database and credentials
3. **Request Validation**: All requests must include valid tenant identification
4. **Error Handling**: Tenant errors return appropriate HTTP status codes without exposing internal details

## Vault Integration

The current implementation includes placeholders for vault integration. To implement vault:

1. Update `superset/utils/tenant.py` in the `_get_tenant_config_from_vault` method
2. Add vault client initialization
3. Configure vault paths for tenant credentials
4. Update environment variables to use vault references

Example vault path structure:

```
secret/tenants/tenant1/database
secret/tenants/tenant2/database
```

## Testing

Use the provided test script to verify the multi-tenant setup:

```bash
python test_multitenant.py
```

This script tests:

- Tenant manager functionality
- HTTP endpoint responses with different tenant headers
- Database configuration retrieval

## Deployment

### Development

1. Update environment variables in `docker/.env`
2. Start the services: `docker-compose -f docker-compose-dev.yml up`
3. The initialization script will create tenant databases automatically

### Production

1. Configure vault integration for secure credential management
2. Set up proper tenant database credentials in vault
3. Update `ALLOWED_TENANTS` with production tenant list
4. Ensure database initialization scripts run during deployment

## Troubleshooting

### Common Issues

1. **"Tenant not configured" error**: Check that the tenant is in `ALLOWED_TENANTS`
2. **Database connection errors**: Verify tenant database credentials
3. **Missing tenant header**: Ensure all requests include `X-Tenant-ID` header

### Debugging

Enable debug logging to see tenant switching:

```python
import logging
logging.getLogger('superset.utils.tenant').setLevel(logging.DEBUG)
```

### Logs to Monitor

- Tenant validation: `superset.middleware.tenant`
- Database switching: `superset.superset_config`
- Tenant configuration: `superset.utils.tenant`

## Future Enhancements

1. **Dynamic Tenant Creation**: API endpoints for creating new tenants
2. **Tenant-Scoped Caching**: Separate cache namespaces per tenant
3. **Tenant Analytics**: Usage metrics per tenant
4. **Tenant-Specific Configuration**: Custom settings per tenant
5. **Multi-Region Support**: Tenant databases in different regions
