FLASK_APP=superset/app.py
# SUPERSET_CONFIG_PATH=/Users/<USER>/Downloads/test/superset-test/superset/superset_config.py
SUPERSET_HOME=/
DATABASE_DB=postgres
DATABASE_HOST=host.docker.internal
DATABASE_PASSWORD=postgres
DATABASE_USER=sushantratnam
DATABASE_DIALECT=postgresql
DATABASE_PORT=5432
MAPBOX_API_KEY=
TENANT_ID=0
SANDBOX_ID=1
REALM=0-admin.India
CLIENT_ID=eMsKSpykyaISGlkQngcY.localt
CLIENT_SECRET=qD2OdvFETYPFL_HKeW7-0_jYtH-xKLsmbHfBU-85
TOKEN_URL=https://sso-pp.zetaapps.in/
USER_INFO_URL=https://sso-pp.zetaapps.in/userInfo
API_URL=https://sso-pp.zetaapps.in/
AUTHORIZE_URL=https://0-0-cipher.mum1-pp.zetaapps.in/sso/authorize
PROTEUS_URL=https://proteus-cipher.mum1-pp.zeta.in/
SCOPE=admin
CRUX_URL=http://host.docker.internal:7005
SECRET_KEY=x1IEZMjzmEqH0k0zfP/YQG5RsQFbQJ7c7uRyBNAkk9fR66Lpkz7WaegA
REDIS_HOST=redis
REDIS_PORT=6379
WEBDRIVER_BASEURL=http://localhost:8088
WEBDRIVER_BASEURL_USER_FRIENDLY=http://localhost:8088
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_MAIL_FROM=
SUPERSET_SECRET_KEY=X5sQ3v9yH@McQfTjWnZr4u7w!z%C*F-JaNdRgUkXp2s5v8
DATA_DIR=/tmp