#!/usr/bin/env python3
"""
Simple test to verify multi-tenant database configuration without external dependencies.
"""

import os

def load_localenv():
    """Load .localenv configuration"""
    config = {}
    localenv_path = '.localenv'
    
    print(f"Looking for .localenv at: {os.path.abspath(localenv_path)}")
    
    if os.path.exists(localenv_path):
        print("✅ .localenv file found")
        with open(localenv_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        print(f"✅ Loaded {len(config)} configuration variables")
    else:
        print("❌ .localenv file not found")
    
    return config

def test_tenant_db_configs():
    """Test tenant database configuration generation"""
    print("\nTesting Tenant Database Configurations")
    print("=" * 50)
    
    # Load configuration
    config = load_localenv()
    
    if not config:
        print("❌ No configuration loaded")
        return
    
    print("\nBase configuration from .localenv:")
    for key, value in config.items():
        if 'PASSWORD' in key:
            print(f"  {key}=***")
        else:
            print(f"  {key}={value}")
    
    # Extract database parameters
    host = config.get('DATABASE_HOST', 'localhost')
    port = config.get('DATABASE_PORT', '5432')
    user = config.get('DATABASE_USER', 'postgres')
    password = config.get('DATABASE_PASSWORD', 'postgres')
    base_db = config.get('DATABASE_DB', 'postgres')
    dialect = config.get('DATABASE_DIALECT', 'postgresql')
    
    print(f"\nBase database configuration:")
    print(f"  Host: {host}")
    print(f"  Port: {port}")
    print(f"  User: {user}")
    print(f"  Base DB: {base_db}")
    print(f"  Dialect: {dialect}")
    
    # Test tenant configurations
    tenants = ['tenant1', 'tenant2']

    print(f"\nTenant database configurations:")
    for tenant in tenants:
        tenant_db = f"{tenant}_superset"  # Use superset suffix to match actual setup

        # Generate database URI
        uri = f"{dialect}://{user}:{password}@{host}:{port}/{tenant_db}"

        print(f"\nTenant: {tenant}")
        print(f"  Database: {tenant_db}")
        print(f"  URI: {dialect}://{user}:***@{host}:{port}/{tenant_db}")
        print(f"  Full URI: {uri}")

def test_route_extraction():
    """Test tenant extraction from routes"""
    print("\nTesting Route-Based Tenant Extraction")
    print("=" * 50)
    
    test_routes = [
        "/tenant/tenant1/dashboard/",
        "/tenant/tenant2/explore/",
        "/tenant/invalid/sqllab/",
        "/health",
        "/static/css/style.css",
        "/tenant/",
        "/tenant/tenant1/",
        "/api/v1/dashboard/",
    ]
    
    allowed_tenants = ['tenant1', 'tenant2']
    
    for route in test_routes:
        print(f"\nRoute: {route}")
        
        # Extract tenant from route
        path_parts = route.strip('/').split('/')
        tenant_id = None
        
        if len(path_parts) >= 2 and path_parts[0] == 'tenant':
            tenant_id = path_parts[1] if path_parts[1] else None
        
        if route.startswith('/tenant/'):
            if tenant_id:
                if tenant_id in allowed_tenants:
                    print(f"  ✅ Valid tenant: {tenant_id}")
                    print(f"  Would use database: {tenant_id}_superset")
                else:
                    print(f"  ❌ Invalid tenant: {tenant_id}")
            else:
                print(f"  ❌ No tenant found in route")
        else:
            print(f"  ⏭️  Non-tenant route (skipped)")

def test_docker_setup():
    """Test Docker setup configuration"""
    print("\nTesting Docker Setup")
    print("=" * 50)
    
    # Check docker-compose file
    compose_file = 'docker-compose-dev.yml'
    if os.path.exists(compose_file):
        print(f"✅ {compose_file} exists")
        
        with open(compose_file, 'r') as f:
            content = f.read()
            if 'POSTGRES_MULTIPLE_DATABASES' in content:
                print("✅ Multi-database configuration found")
                
                # Extract the database list
                for line in content.split('\n'):
                    if 'POSTGRES_MULTIPLE_DATABASES' in line:
                        print(f"  {line.strip()}")
            else:
                print("❌ Multi-database configuration not found")
    else:
        print(f"❌ {compose_file} not found")
    
    # Check .env file
    env_file = 'docker/.env'
    if os.path.exists(env_file):
        print(f"✅ {env_file} exists")
        
        with open(env_file, 'r') as f:
            content = f.read()
            if 'MULTI_TENANT_ENABLED' in content:
                print("✅ Multi-tenant configuration found")
                
                for line in content.split('\n'):
                    if 'MULTI_TENANT_ENABLED' in line or 'ALLOWED_TENANTS' in line:
                        print(f"  {line.strip()}")
            else:
                print("❌ Multi-tenant configuration not found")
    else:
        print(f"❌ {env_file} not found")
    
    # Check initialization script
    init_script = 'docker/docker-entrypoint-initdb.d/multi-tenant-init.sh'
    if os.path.exists(init_script):
        print(f"✅ {init_script} exists")
        print("✅ Database initialization script ready")
    else:
        print(f"❌ {init_script} not found")

if __name__ == "__main__":
    print("Simple Multi-Tenant Database Test")
    print("=" * 50)
    
    test_tenant_db_configs()
    test_route_extraction()
    test_docker_setup()
    
    print("\n" + "=" * 50)
    print("✅ Configuration test completed!")
    print("\nNext steps:")
    print("1. Start Docker: docker-compose -f docker-compose-dev.yml up")
    print("2. Test URLs: http://localhost:8088/tenant/tenant1/dashboard/")
    print("3. Check logs for database switching messages")
