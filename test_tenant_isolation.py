#!/usr/bin/env python3
"""
Test tenant database isolation by verifying each tenant sees only their data.
"""

import subprocess
import json

def run_sql_query(database, query):
    """Run SQL query in specific database via Docker."""
    try:
        result = subprocess.run([
            'docker', 'exec', 'superset_db',
            'psql', '-U', 'superset', '-d', database,
            '-c', query, '-t', '-A'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, result.stderr.strip()
            
    except Exception as e:
        return False, str(e)

def test_tenant_isolation():
    """Test that each tenant database contains only their data."""
    print("🧪 Testing Tenant Database Isolation")
    print("=" * 60)
    
    # Test query to get customer data
    test_query = """
    SELECT 
        current_database() as db,
        c.name,
        c.tenant_info,
        COUNT(o.id) as orders
    FROM customers c
    LEFT JOIN orders o ON c.id = o.customer_id
    GROUP BY c.id, c.name, c.tenant_info
    ORDER BY c.name;
    """
    
    databases_to_test = [
        ('tenant1_superset', 'Tenant 1'),
        ('tenant2_superset', 'Tenant 2'),
        ('postgres', 'Main Database'),
        ('superset', 'Superset Metadata')
    ]
    
    isolation_verified = True
    
    for db_name, description in databases_to_test:
        print(f"\n📊 Testing {description} ({db_name}):")
        print("-" * 40)
        
        success, result = run_sql_query(db_name, test_query)
        
        if success and result:
            lines = result.split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 4:
                        db, name, tenant_info, orders = [p.strip() for p in parts]
                        print(f"  👤 {name} ({tenant_info}) - {orders} orders")
                    
        elif 'does not exist' in result:
            print(f"  ✅ No customer tables (expected for {description})")
            
        else:
            print(f"  ❌ Error: {result}")
            isolation_verified = False
    
    return isolation_verified

def verify_cross_tenant_access():
    """Verify tenants cannot access each other's data."""
    print(f"\n🔒 Testing Cross-Tenant Access Prevention")
    print("=" * 60)
    
    # Try to access tenant2 data from tenant1 database
    cross_access_query = """
    SELECT * FROM tenant2_superset.customers LIMIT 1;
    """
    
    print("Attempting to access tenant2 data from tenant1 database...")
    success, result = run_sql_query('tenant1_superset', cross_access_query)
    
    if not success and ('does not exist' in result or 'permission denied' in result):
        print("✅ Cross-tenant access properly blocked")
        return True
    else:
        print(f"❌ Cross-tenant access not properly blocked: {result}")
        return False

def test_database_switching_simulation():
    """Simulate how the application switches databases."""
    print(f"\n🔄 Simulating Application Database Switching")
    print("=" * 60)
    
    # Simulate different tenant requests
    tenant_requests = [
        ('tenant1', 'tenant1_superset'),
        ('tenant2', 'tenant2_superset'),
    ]
    
    for tenant_id, expected_db in tenant_requests:
        print(f"\n🌐 Simulating request: /tenant/{tenant_id}/dashboard/")
        
        # Query to verify we're in the right database
        db_check_query = "SELECT current_database();"
        success, result = run_sql_query(expected_db, db_check_query)
        
        if success and result == expected_db:
            print(f"  ✅ Correctly connected to {expected_db}")
            
            # Get tenant-specific data
            data_query = "SELECT COUNT(*) FROM customers;"
            success, count = run_sql_query(expected_db, data_query)
            
            if success:
                print(f"  📊 Found {count} customers in {tenant_id} database")
            else:
                print(f"  ❌ Could not query customer data: {count}")
        else:
            print(f"  ❌ Database connection failed: {result}")

def main():
    print("Multi-Tenant Database Isolation Test")
    print("=" * 60)
    
    # Check if container is running
    try:
        subprocess.run(['docker', 'exec', 'superset_db', 'echo', 'test'], 
                      capture_output=True, timeout=5, check=True)
    except:
        print("❌ PostgreSQL container not running")
        print("Start with: docker-compose -f docker-compose-dev.yml up -d db")
        return
    
    # Run tests
    isolation_ok = test_tenant_isolation()
    cross_access_ok = verify_cross_tenant_access()
    
    # Simulate application behavior
    test_database_switching_simulation()
    
    # Summary
    print(f"\n" + "=" * 60)
    print("🎯 TEST SUMMARY")
    print("=" * 60)
    
    if isolation_ok and cross_access_ok:
        print("✅ All isolation tests PASSED")
        print("✅ Tenant data is properly isolated")
        print("✅ Cross-tenant access is blocked")
        print("\n🎉 Multi-tenant system is working correctly!")
    else:
        print("❌ Some tests FAILED")
        print("❌ Tenant isolation may be compromised")
    
    print(f"\n📋 Next Steps:")
    print("1. Verify data in DBeaver by connecting to each tenant database")
    print("2. Run queries to confirm each tenant sees only their data")
    print("3. Test the Superset application with tenant URLs")

if __name__ == "__main__":
    main()
