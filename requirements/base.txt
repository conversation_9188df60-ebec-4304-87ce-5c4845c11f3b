# SHA1:85649679306ea016e401f37adfbad832028d2e5f
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-e file:.
    # via -r requirements/base.in
alembic==1.6.5
    # via flask-migrate
amqp==5.1.1
    # via kombu
apispec[yaml]==6.3.0
    # via flask-appbuilder
apsw==********
    # via shillelagh
async-timeout==4.0.2
    # via redis
attrs==23.1.0
    # via
    #   cattrs
    #   jsonschema
    #   requests-cache
babel==2.9.1
    # via flask-babel
backoff==1.11.1
    # via apache-superset
bcrypt==4.0.1
    # via paramiko
billiard==4.2.0
    # via celery
bottleneck==1.3.7
    # via pandas
brotli==1.0.9
    # via flask-compress
cachelib==0.9.0
    # via
    #   flask-caching
    #   flask-session
cachetools==5.3.2
    # via google-auth
cattrs==23.2.1
    # via requests-cache
celery==5.3.6
    # via apache-superset
certifi==2023.7.22
    # via requests
cffi==1.15.1
    # via
    #   cryptography
    #   pynacl
charset-normalizer==3.2.0
    # via requests
click==8.1.3
    # via
    #   apache-superset
    #   celery
    #   click-didyoumean
    #   click-option-group
    #   click-plugins
    #   click-repl
    #   flask
    #   flask-appbuilder
click-didyoumean==0.3.0
    # via celery
click-option-group==0.5.5
    # via apache-superset
click-plugins==1.1.1
    # via celery
click-repl==0.2.0
    # via celery
colorama==0.4.6
    # via
    #   apache-superset
    #   flask-appbuilder
cron-descriptor==1.2.24
    # via apache-superset
croniter==1.0.15
    # via apache-superset
cryptography==42.0.4
    # via
    #   apache-superset
    #   paramiko
deprecated==1.2.13
    # via limits
deprecation==2.1.0
    # via apache-superset
dnspython==2.1.0
    # via email-validator
email-validator==1.1.3
    # via flask-appbuilder
flask==2.2.5
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-babel
    #   flask-caching
    #   flask-compress
    #   flask-jwt-extended
    #   flask-limiter
    #   flask-login
    #   flask-migrate
    #   flask-session
    #   flask-sqlalchemy
    #   flask-wtf
flask-appbuilder==4.4.1
    # via apache-superset
flask-babel==1.0.0
    # via flask-appbuilder
flask-caching==2.1.0
    # via apache-superset
flask-compress==1.13
    # via apache-superset
flask-jwt-extended==4.3.1
    # via flask-appbuilder
flask-limiter==3.3.1
    # via flask-appbuilder
flask-login==0.6.3
    # via
    #   apache-superset
    #   flask-appbuilder
flask-migrate==3.1.0
    # via apache-superset
flask-session==0.5.0
    # via apache-superset
flask-sqlalchemy==2.5.1
    # via
    #   flask-appbuilder
    #   flask-migrate
flask-talisman==1.0.0
    # via apache-superset
flask-wtf==1.2.1
    # via
    #   apache-superset
    #   flask-appbuilder
func-timeout==4.3.5
    # via apache-superset
geographiclib==1.52
    # via geopy
geopy==2.2.0
    # via apache-superset
google-auth==2.27.0
    # via shillelagh
greenlet==3.0.3
    # via
    #   shillelagh
    #   sqlalchemy
gunicorn==21.2.0
    # via apache-superset
hashids==1.3.1
    # via apache-superset
holidays==0.25
    # via apache-superset
humanize==3.11.0
    # via apache-superset
idna==3.2
    # via
    #   email-validator
    #   requests
importlib-metadata==6.6.0
    # via apache-superset
importlib-resources==5.12.0
    # via limits
isodate==0.6.0
    # via apache-superset
itsdangerous==2.1.2
    # via
    #   flask
    #   flask-wtf
jinja2==3.1.3
    # via
    #   flask
    #   flask-babel
jsonschema==4.17.3
    # via flask-appbuilder
kombu==5.3.4
    # via celery
korean-lunar-calendar==0.3.1
    # via holidays
limits==3.4.0
    # via flask-limiter
llvmlite==0.40.1
    # via numba
mako==1.2.4
    # via
    #   alembic
    #   apache-superset
markdown==3.3.4
    # via apache-superset
markdown-it-py==2.2.0
    # via rich
markupsafe==2.1.1
    # via
    #   jinja2
    #   mako
    #   werkzeug
    #   wtforms
marshmallow==3.19.0
    # via
    #   flask-appbuilder
    #   marshmallow-sqlalchemy
marshmallow-sqlalchemy==0.23.1
    # via flask-appbuilder
mdurl==0.1.2
    # via markdown-it-py
msgpack==1.0.2
    # via apache-superset
nh3==0.2.11
    # via apache-superset
numba==0.57.1
    # via pandas
numexpr==2.9.0
    # via
    #   -r requirements/base.in
    #   pandas
numpy==1.23.5
    # via
    #   apache-superset
    #   bottleneck
    #   numba
    #   numexpr
    #   pandas
    #   pyarrow
ordered-set==4.1.0
    # via flask-limiter
packaging==23.1
    # via
    #   apache-superset
    #   apispec
    #   deprecation
    #   gunicorn
    #   limits
    #   marshmallow
    #   shillelagh
pandas[performance]==2.0.3
    # via apache-superset
paramiko==3.4.0
    # via
    #   apache-superset
    #   sshtunnel
parsedatetime==2.6
    # via apache-superset
pgsanity==0.2.9
    # via apache-superset
platformdirs==3.8.1
    # via requests-cache
polyline==2.0.0
    # via apache-superset
prison==0.2.1
    # via flask-appbuilder
prompt-toolkit==3.0.38
    # via click-repl
pyarrow==14.0.1
    # via apache-superset
pyasn1==0.5.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.3.0
    # via google-auth
pycparser==2.20
    # via cffi
pygments==2.15.0
    # via rich
pyjwt==2.4.0
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-jwt-extended
pynacl==1.5.0
    # via paramiko
pyparsing==3.0.6
    # via apache-superset
pyrsistent==0.19.3
    # via jsonschema
python-dateutil==2.8.2
    # via
    #   alembic
    #   apache-superset
    #   celery
    #   croniter
    #   flask-appbuilder
    #   holidays
    #   pandas
    #   shillelagh
python-dotenv==0.19.0
    # via apache-superset
python-editor==1.0.4
    # via alembic
python-geohash==0.8.5
    # via apache-superset
pytz==2021.3
    # via
    #   babel
    #   flask-babel
    #   pandas
pyyaml==6.0.1
    # via
    #   apache-superset
    #   apispec
redis==4.5.4
    # via apache-superset
requests==2.31.0
    # via
    #   requests-cache
    #   shillelagh
requests-cache==1.1.1
    # via shillelagh
rich==13.3.4
    # via flask-limiter
rsa==4.9
    # via google-auth
selenium==3.141.0
    # via apache-superset
shillelagh[gsheetsapi]==1.2.10
    # via apache-superset
shortid==0.1.2
    # via apache-superset
simplejson==3.17.3
    # via apache-superset
six==1.16.0
    # via
    #   click-repl
    #   isodate
    #   prison
    #   python-dateutil
    #   url-normalize
    #   wtforms-json
slack-sdk==3.21.3
    # via apache-superset
sqlalchemy==1.4.36
    # via
    #   alembic
    #   apache-superset
    #   flask-appbuilder
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   shillelagh
    #   sqlalchemy-utils
sqlalchemy-utils==0.38.3
    # via
    #   apache-superset
    #   flask-appbuilder
sqlglot==23.0.2
    # via apache-superset
sqlparse==0.4.4
    # via apache-superset
sshtunnel==0.4.0
    # via apache-superset
tabulate==0.8.9
    # via apache-superset
typing-extensions==4.4.0
    # via
    #   apache-superset
    #   flask-limiter
    #   limits
    #   shillelagh
tzdata==2023.3
    # via
    #   celery
    #   pandas
url-normalize==1.4.3
    # via requests-cache
urllib3==1.26.18
    # via
    #   -r requirements/base.in
    #   requests
    #   requests-cache
    #   selenium
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.5
    # via prompt-toolkit
werkzeug==3.0.1
    # via
    #   -r requirements/base.in
    #   flask
    #   flask-appbuilder
    #   flask-jwt-extended
    #   flask-login
wrapt==1.15.0
    # via deprecated
wtforms==2.3.3
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-wtf
    #   wtforms-json
wtforms-json==0.3.5
    # via apache-superset
xlsxwriter==3.0.7
    # via apache-superset
zipp==3.15.0
    # via importlib-metadata
sqlalchemy-redshift==0.8.14
PyAthena==3.14.1
statsd==4.0.1
waitress==3.0.2
prometheus_client==0.22.1
# The following packages are considered to be unsafe in a requirements file:
# setuptools

