#!/usr/bin/env python3
"""
Simulate how the multi-tenant application switches databases.
This demonstrates the exact flow that happens in Superset.
"""

import psycopg2
import sys

def get_database_connection(tenant_id=None):
    """
    Simulate the database connection logic from the multi-tenant system.
    This mimics what happens in the DB_CONNECTION_MUTATOR.
    """
    # Base configuration from .localenv
    base_config = {
        'host': 'localhost',  # host.docker.internal from container perspective
        'port': 5432,
        'user': 'sushantratnam',
        'password': 'postgres',
    }
    
    if tenant_id:
        # Multi-tenant mode: switch to tenant-specific database
        database = f"{tenant_id}_superset"
        print(f"🔄 Switching to tenant database: {database}")
    else:
        # Default mode: use main database
        database = "postgres"
        print(f"🔄 Using default database: {database}")
    
    try:
        conn = psycopg2.connect(
            host=base_config['host'],
            port=base_config['port'],
            user=base_config['user'],
            password=base_config['password'],
            database=database
        )
        print(f"✅ Connected to database: {database}")
        return conn, database
    except Exception as e:
        print(f"❌ Failed to connect to {database}: {e}")
        return None, database

def simulate_request(url_path):
    """
    Simulate processing a request and extracting tenant information.
    This mimics what happens in the middleware.
    """
    print(f"\n🌐 Processing request: {url_path}")
    
    # Extract tenant from URL (middleware logic)
    path_parts = url_path.strip('/').split('/')
    tenant_id = None
    
    if len(path_parts) >= 2 and path_parts[0] == 'tenant':
        tenant_id = path_parts[1]
        print(f"🏷️  Extracted tenant: {tenant_id}")
        
        # Validate tenant (from ALLOWED_TENANTS)
        allowed_tenants = ['tenant1', 'tenant2']
        if tenant_id not in allowed_tenants:
            print(f"❌ Invalid tenant: {tenant_id}")
            return None
        print(f"✅ Valid tenant: {tenant_id}")
    else:
        print("ℹ️  No tenant in URL - using default database")
    
    return tenant_id

def query_customer_data(conn, database):
    """
    Query customer data to demonstrate tenant isolation.
    """
    try:
        cursor = conn.cursor()
        
        # Check if customers table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'customers'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print(f"📋 No customers table in {database}")
            return
        
        # Query customer data
        cursor.execute("""
            SELECT 
                c.name,
                c.tenant_info,
                COUNT(o.id) as order_count,
                COALESCE(SUM(o.amount), 0) as total_spent
            FROM customers c
            LEFT JOIN orders o ON c.id = o.customer_id
            GROUP BY c.id, c.name, c.tenant_info
            ORDER BY c.name;
        """)
        
        results = cursor.fetchall()
        
        if results:
            print(f"📊 Customer data in {database}:")
            print("   Name                | Tenant Info        | Orders | Total Spent")
            print("   " + "-" * 65)
            for row in results:
                name, tenant_info, order_count, total_spent = row
                print(f"   {name:<18} | {tenant_info:<18} | {order_count:>6} | ${total_spent:>9.2f}")
        else:
            print(f"📋 No customer data in {database}")
            
        cursor.close()
        
    except Exception as e:
        print(f"❌ Error querying {database}: {e}")

def test_multi_tenant_flow():
    """
    Test the complete multi-tenant flow with different URLs.
    """
    print("🧪 Testing Multi-Tenant Database Flow")
    print("=" * 60)
    
    # Test scenarios
    test_urls = [
        "/tenant/tenant1/dashboard/",
        "/tenant/tenant2/explore/", 
        "/tenant/invalid/dashboard/",
        "/health",
        "/dashboard/list/"
    ]
    
    for url in test_urls:
        # Step 1: Extract tenant from URL
        tenant_id = simulate_request(url)
        
        if tenant_id is None and url.startswith('/tenant/'):
            print("🚫 Request blocked - invalid tenant")
            continue
        
        # Step 2: Get database connection
        conn, database = get_database_connection(tenant_id)
        
        if conn:
            # Step 3: Query data (simulate Superset operations)
            query_customer_data(conn, database)
            conn.close()
        
        print("-" * 60)

if __name__ == "__main__":
    print("Multi-Tenant Application Flow Simulation")
    print("This demonstrates how Superset switches databases based on tenant context")
    print()
    
    test_multi_tenant_flow()
    
    print("\n🎯 Summary:")
    print("- Each tenant gets isolated database access")
    print("- URL determines which database to use")
    print("- Data is completely separated between tenants")
    print("- Same application code works for all tenants")
    
    print("\n📝 To test in DBeaver:")
    print("1. Connect to tenant1_superset and tenant2_superset")
    print("2. Create test data using setup_test_data.sql")
    print("3. Query each database to see isolated data")
    print("4. Verify tenants cannot see each other's data")
