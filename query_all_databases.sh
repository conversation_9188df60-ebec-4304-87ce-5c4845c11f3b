#!/bin/bash

# <PERSON><PERSON>t to run the same query across all databases to test tenant isolation

echo "🔍 Testing Tenant Database Isolation"
echo "===================================="

# List of databases to test
databases=("postgres" "superset" "tenant1_superset" "tenant2_superset")

# Test query
query="SELECT current_database() as db_name;"

echo ""
echo "📊 Database Connectivity Test:"
echo "------------------------------"

for db in "${databases[@]}"; do
    echo -n "Testing $db... "
    result=$(docker exec superset_db psql -U superset -d "$db" -t -c "$query" 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "✅ Connected"
    else
        echo "❌ Failed"
    fi
done

echo ""
echo "📋 Customer Data Test:"
echo "----------------------"

# Query for customer data
customer_query="
SELECT 
    current_database() as database,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customers') 
        THEN CONCAT('Has ', (SELECT COUNT(*) FROM customers), ' customers')
        ELSE 'No customer table'
    END as status;
"

for db in "${databases[@]}"; do
    echo ""
    echo "🗄️  Database: $db"
    echo "   $(docker exec superset_db psql -U superset -d "$db" -t -c "$customer_query" 2>/dev/null | tr -d ' ')"
done

echo ""
echo "👥 Detailed Customer Data:"
echo "--------------------------"

# Detailed customer query for tenant databases
detailed_query="
SELECT 
    c.name,
    c.tenant_info,
    COUNT(o.id) as orders,
    COALESCE(SUM(o.amount), 0) as total_spent
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.name, c.tenant_info
ORDER BY c.name;
"

for db in "tenant1_superset" "tenant2_superset"; do
    echo ""
    echo "🏢 $db:"
    docker exec superset_db psql -U superset -d "$db" -c "$detailed_query" 2>/dev/null || echo "   No customer data found"
done

echo ""
echo "🎯 Isolation Test Summary:"
echo "---------------------------"
echo "✅ Each tenant database should show different customers"
echo "✅ Main databases (postgres/superset) should have no customer tables"
echo "✅ Tenants cannot see each other's data"
