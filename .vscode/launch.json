{"version": "0.2.0", "configurations": [{"name": "Superset with <PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/superset/waitress_entry.py", "env": {"FLASK_APP": "superset/app.py", "SUPERSET_HOME": "/tmp/superset_home", "SUPERSET_CONFIG_PATH": "${workspaceFolder}/superset/superset_config.py", "DATABASE_DB": "supersetDB", "DATABASE_HOST": "localhost", "DATABASE_PASSWORD": "postgres", "DATABASE_USER": "postgres", "DATABASE_DIALECT": "postgresql", "DATABASE_PORT": "5432", "MAPBOX_API_KEY": "", "TENANT_ID": "0", "SANDBOX_ID": "1", "REALM": "0-admin.India", "CLIENT_ID": "eMsKSpykyaISGlkQngcY.localt", "CLIENT_SECRET": "qD2OdvFETYPFL_HKeW7-0_jYtH-xKLsmbHfBU-85", "TOKEN_URL": "https://sso-pp.zetaapps.in/", "USER_INFO_URL": "https://sso-pp.zetaapps.in/userInfo", "API_URL": "https://sso-pp.zetaapps.in/", "AUTHORIZE_URL": "https://0-0-cipher.mum1-pp.zetaapps.in/sso/authorize", "PROTEUS_URL": "https://proteus-cipher.mum1-pp.zeta.in/", "SCOPE": "admin", "CRUX_URL": "http://localhost:7005", "SECRET_KEY": "x1IEZMjzmEqH0k0zfP/YQG5RsQFbQJ7c7uRyBNAkk9fR66Lpkz7WaegA", "REDIS_HOST": "localhost", "REDIS_PORT": "6379", "WEBDRIVER_BASEURL": "http://localhost:8088", "WEBDRIVER_BASEURL_USER_FRIENDLY": "http://localhost:8088", "SMTP_HOST": "smtp.gmail.com", "SMTP_PORT": "587", "SMTP_USER": "", "SMTP_PASSWORD": "", "SMTP_MAIL_FROM": "", "DATA_DIR": "/tmp"}, "args": [], "console": "integratedTerminal", "justMyCode": false}]}