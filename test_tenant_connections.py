#!/usr/bin/env python3
"""
Test tenant database connections using Docker exec.
"""

import subprocess
import json

def run_psql_command(database, command):
    """Run a psql command in the Docker container."""
    try:
        result = subprocess.run([
            'docker', 'exec', 'superset_db', 
            'psql', '-U', 'superset', '-d', database, '-c', command
        ], capture_output=True, text=True, timeout=10)
        
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def test_database_connection(database):
    """Test connection to a specific database."""
    print(f"\nTesting database: {database}")
    print("-" * 40)
    
    # Test basic connection
    success, stdout, stderr = run_psql_command(database, "SELECT current_database(), version();")
    
    if success:
        print(f"✅ Successfully connected to {database}")
        lines = stdout.strip().split('\n')
        for line in lines:
            if database in line or 'PostgreSQL' in line:
                print(f"   {line.strip()}")
    else:
        print(f"❌ Failed to connect to {database}")
        if stderr:
            print(f"   Error: {stderr.strip()}")
    
    return success

def test_database_isolation():
    """Test that tenant databases are isolated."""
    print(f"\nTesting database isolation...")
    print("-" * 40)
    
    databases = ['tenant1_superset', 'tenant2_superset']
    
    for db in databases:
        print(f"\nTesting isolation for: {db}")
        
        # Create a test table
        table_name = f"test_table_{db.replace('_', '')}"
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id SERIAL PRIMARY KEY,
            tenant_name VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        success, stdout, stderr = run_psql_command(db, create_sql)
        if success:
            print(f"   ✅ Created test table {table_name}")
            
            # Insert test data
            insert_sql = f"INSERT INTO {table_name} (tenant_name) VALUES ('{db}');"
            success, stdout, stderr = run_psql_command(db, insert_sql)
            if success:
                print(f"   ✅ Inserted test data")
                
                # Query the data
                select_sql = f"SELECT * FROM {table_name};"
                success, stdout, stderr = run_psql_command(db, select_sql)
                if success:
                    print(f"   ✅ Retrieved data:")
                    lines = stdout.strip().split('\n')
                    for line in lines[2:-1]:  # Skip header and footer
                        if line.strip() and '|' in line:
                            print(f"      {line.strip()}")
            else:
                print(f"   ❌ Failed to insert data: {stderr}")
        else:
            print(f"   ❌ Failed to create table: {stderr}")

def test_cross_database_isolation():
    """Test that tenants cannot access each other's data."""
    print(f"\nTesting cross-database isolation...")
    print("-" * 40)
    
    # Try to access tenant2 table from tenant1 database
    cross_access_sql = "SELECT * FROM test_tabletenant2superset;"
    success, stdout, stderr = run_psql_command('tenant1_superset', cross_access_sql)
    
    if not success and 'does not exist' in stderr:
        print("✅ Cross-database isolation working - tenant1 cannot access tenant2 tables")
    elif not success:
        print(f"✅ Cross-database access blocked: {stderr.strip()}")
    else:
        print("❌ Cross-database isolation may be compromised")

def main():
    print("Multi-Tenant Database Connection Test")
    print("=" * 50)
    
    # Test main databases
    databases = ['postgres', 'superset', 'tenant1_superset', 'tenant2_superset']
    
    print("Testing database connections...")
    
    all_success = True
    for db in databases:
        success = test_database_connection(db)
        if not success:
            all_success = False
    
    if all_success:
        print(f"\n✅ All database connections successful!")
        
        # Test isolation
        test_database_isolation()
        test_cross_database_isolation()
        
        print(f"\n" + "=" * 50)
        print("🎉 Multi-tenant database setup is working!")
        print("=" * 50)
        
        print("\nDatabase summary:")
        print("- postgres: Main PostgreSQL database")
        print("- superset: Main Superset metadata database")
        print("- tenant1_superset: Tenant 1 isolated database")
        print("- tenant2_superset: Tenant 2 isolated database")
        
        print("\nNext steps:")
        print("1. Start Superset: docker-compose -f docker-compose-dev.yml up superset")
        print("2. Test tenant URLs: http://localhost:8088/tenant/tenant1/dashboard/")
        
    else:
        print(f"\n❌ Some database connections failed")
        print("Make sure the database container is running:")
        print("  docker-compose -f docker-compose-dev.yml up -d db")

if __name__ == "__main__":
    main()
