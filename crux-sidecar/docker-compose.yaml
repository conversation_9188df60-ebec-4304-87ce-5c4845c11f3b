version: "3.7"

services:
  crux-sidecar-local:
    container_name: crux-sidecar-local
    # image: 813361731051.dkr.ecr.ap-south-1.amazonaws.com/crux-sidecar:7.2.2
    build:
      context: .
    network_mode: host
    environment:
      server_host: localhost
      server_port: 7005
      server_timeout: 15000
      javaXmx: 512M
      javaXms: 512M
      JMX_PORT: "63129"
      JMX_RMI_PORT: "33333"
      JMX_PROMETHEUS_JAVAAGENT_METRICS_PORT: "44444"
      JMX_PROMETHEUS_JAVAAGENT_CONFIG_FILE: /prometheus/jmx-exporter-config.yaml
      KINESIS_ACCESSKEY: vault:secrets/data/zone/common/logging#KINESIS_ACCESSKEY
      KINESIS_SECRETKEY: vault:secrets/data/zone/common/logging#KINESIS_SECRETKEY
      KINESIS_REGION: ap-south-1
      app.name: superset-reportcenter
      crux-llt.cluster.name: zeus
      crux-llt.god.oauth.app-auth-profile-id: SO-GuPYMaNxVj62_EYMBbQ==
      crux-llt.god.oauth.app-private-key: MEECAQAwEwYHKoZIzj0CAQYIKoZIzj0DAQcEJzAlAgEBBCBlZ3Y9jAsywj92z1qKaiGaTRq0xoXDKlPuhk6lCnZzfA==
      crux-llt.god.oauth.app-public-key: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEebJNLo62Po+jRWypNH6bPtu9t6/3Gs+VX8W4kB975r8ChAovCR+hhadbXo7e6nZP15g4mH1mIn9sZTbntKUKpg==
      crux-llt.god.oauth.app-resource-cert: |
        -----BEGIN CERTIFICATE-----
        MIIBsDCCAVegAwIBAgIVANDszn7QCNaj7TtILTaP4oOQk+E/MAoGCCqGSM49BAMC
        MDYxNDAyBgNVBAMMK1NPLUd1UFlNYU54Vmo2Ml9FWU1CYlE9PUBhdXRoLjAtYWRt
        aW4uSW5kaWEwHhcNMjQwNTAzMTQ1OTQwWhcNMjQwNTEzMTQ1OTQwWjA2MTQwMgYD
        VQQDDCtTTy1HdVBZTWFOeFZqNjJfRVlNQmJRPT1AYXV0aC4wLWFkbWluLkluZGlh
        MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEebJNLo62Po+jRWypNH6bPtu9t6/3
        Gs+VX8W4kB975r8ChAovCR+hhadbXo7e6nZP15g4mH1mIn9sZTbntKUKpqNCMEAw
        HQYDVR0OBBYEFMNn6WfYIyw/i7TV7DgsJ7QU71kUMA4GA1UdDwEB/wQEAwIBBjAP
        BgNVHRMBAf8EBTADAQH/MAoGCCqGSM49BAMCA0cAMEQCIEJ6SQcLZuL1IGp3nYFB
        L0jpfDfDbA6OvqaG3jVlP61tAiAoNzoABSDulJpZTioudtn2bXbtP0dK2bRm6pDG
        WIpjmw==
        -----END CERTIFICATE-----
      crux-llt.god.oauth.client-id: advFjMTMVESTChLxTLNw.zeus_s
      crux-llt.god.oauth.client-secret: YN0gC29pGi9VY76CsiES4UHlFvGsxsahZujmAZf5
      crux-llt.god.oauth.domain-id: 0-admin.India
      crux-llt.god.oauth.scope: admin
      DELTA_V2_URL: https://elenchos.internal.mum1-pp.zetaapps.in/delta/api/v2/
      DELTA_V2_ENCRYPTION_BASE64SECRET: q/FuC2knanE8Y12kvhVB02ZWlOMQ7wfyN4MXM2EfpUY=
      SSO_BASE_URL: https://sso-pp.zetaapps.in
      CLUSTER_NAME: zeus
      OTEL_ENABLED: false
      JOLOKIA_JAVAAGENT_ENABLED: false
      JMX_PROMETHEUS_JAVAAGENT_ENABLED: false
      environment: local
      APP_NAME: configuration-operator
      APP: configuration-operator
      LOGGING_PIPELINE: KINESIS_AND_SOUT
