#!/usr/bin/env python3
"""
Test actual database connections for different tenants.
"""

import sys
import os
import psycopg2
from psycopg2 import sql

def load_localenv():
    """Load .localenv configuration"""
    config = {}
    localenv_path = '.localenv'
    
    if os.path.exists(localenv_path):
        with open(localenv_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    
    return config

def test_database_connection(host, port, user, password, database):
    """Test connection to a specific database"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        cursor.execute("SELECT current_database();")
        current_db = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return True, f"Connected to {current_db}, PostgreSQL version: {version[:50]}..."
        
    except Exception as e:
        return False, str(e)

def test_tenant_databases():
    """Test connections to tenant databases"""
    print("Testing Tenant Database Connections")
    print("=" * 50)
    
    # Load configuration
    config = load_localenv()
    
    if not config:
        print("❌ Could not load .localenv file")
        return
    
    print("Configuration from .localenv:")
    for key, value in config.items():
        if 'PASSWORD' in key:
            print(f"  {key}=***")
        else:
            print(f"  {key}={value}")
    
    # Database connection parameters
    host = config.get('DATABASE_HOST', 'localhost')
    port = int(config.get('DATABASE_PORT', 5432))
    user = config.get('DATABASE_USER', 'postgres')
    password = config.get('DATABASE_PASSWORD', 'postgres')
    base_db = config.get('DATABASE_DB', 'postgres')
    
    # Test main database
    print(f"\nTesting main database: {base_db}")
    print("-" * 40)
    success, message = test_database_connection(host, port, user, password, base_db)
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
    
    # Test tenant databases
    tenants = ['tenant1', 'tenant2']
    
    for tenant in tenants:
        tenant_db = f"{tenant}_{base_db}"
        print(f"\nTesting tenant database: {tenant_db}")
        print("-" * 40)
        
        success, message = test_database_connection(host, port, user, password, tenant_db)
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            print(f"   Note: Database {tenant_db} may not exist yet.")
            print(f"   Run: docker-compose -f docker-compose-dev.yml up")

def create_test_tables():
    """Create test tables in tenant databases to verify isolation"""
    print("\nTesting Database Isolation")
    print("=" * 50)
    
    config = load_localenv()
    if not config:
        return
    
    host = config.get('DATABASE_HOST', 'localhost')
    port = int(config.get('DATABASE_PORT', 5432))
    user = config.get('DATABASE_USER', 'postgres')
    password = config.get('DATABASE_PASSWORD', 'postgres')
    base_db = config.get('DATABASE_DB', 'postgres')
    
    tenants = ['tenant1', 'tenant2']
    
    for tenant in tenants:
        tenant_db = f"{tenant}_{base_db}"
        print(f"\nTesting isolation for: {tenant_db}")
        
        try:
            conn = psycopg2.connect(
                host=host, port=port, user=user, 
                password=password, database=tenant_db
            )
            
            cursor = conn.cursor()
            
            # Create a test table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {tenant}_test_table (
                    id SERIAL PRIMARY KEY,
                    tenant_name VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Insert test data
            cursor.execute(f"""
                INSERT INTO {tenant}_test_table (tenant_name) 
                VALUES (%s) ON CONFLICT DO NOTHING;
            """, (tenant,))
            
            # Query the data
            cursor.execute(f"SELECT * FROM {tenant}_test_table;")
            rows = cursor.fetchall()
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"✅ Created test table and inserted data")
            print(f"   Rows in {tenant}_test_table: {len(rows)}")
            for row in rows:
                print(f"   {row}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("Multi-Tenant Database Connection Test")
    print("=" * 50)
    
    test_tenant_databases()
    
    # Only run isolation test if user confirms
    response = input("\nDo you want to test database isolation (creates test tables)? (y/N): ")
    if response.lower() == 'y':
        create_test_tables()
    
    print("\n" + "=" * 50)
    print("Database testing completed!")
    print("\nTo start the multi-tenant environment:")
    print("  docker-compose -f docker-compose-dev.yml up")
