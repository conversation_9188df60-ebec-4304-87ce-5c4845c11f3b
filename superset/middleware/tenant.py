# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

"""Multi-tenant middleware for Superset."""

import logging
from typing import Callable, Any
from flask import Flask, request, jsonify, g
from werkzeug.wrappers import Response
from superset.utils.tenant import tenant_manager, TenantNotFoundError

logger = logging.getLogger(__name__)

class TenantMiddleware:
    """WSGI middleware for handling multi-tenant requests."""
    
    def __init__(self, app: Flask):
        self.app = app
        self.wsgi_app = app.wsgi_app
        app.wsgi_app = self
        
        # Register before_request handler
        app.before_request(self.before_request)
        
        # Register error handler for tenant errors
        app.errorhandler(TenantNotFoundError)(self.handle_tenant_error)
    
    def __call__(self, environ: dict, start_response: Callable) -> Any:
        """WSGI application entry point."""
        return self.wsgi_app(environ, start_response)
    
    def before_request(self):
        """Process tenant context before each request."""
        # Skip tenant validation for certain endpoints
        if self._should_skip_tenant_validation():
            return
        
        try:
            # Extract and validate tenant from request
            tenant_id = tenant_manager.get_current_tenant()
            
            if tenant_id:
                logger.debug(f"Processing request for tenant: {tenant_id}")
                # Tenant is now stored in g.tenant_id by tenant_manager
            else:
                # No tenant header provided
                logger.warning("No tenant header provided in request")
                return self._create_error_response(
                    "Tenant identification required. Please provide X-Tenant-ID header.",
                    400
                )
                
        except TenantNotFoundError as e:
            logger.error(f"Tenant validation failed: {str(e)}")
            return self._create_error_response(str(e), 400)
        except Exception as e:
            logger.error(f"Unexpected error in tenant middleware: {str(e)}")
            return self._create_error_response("Internal server error", 500)
    
    def _should_skip_tenant_validation(self) -> bool:
        """Determine if tenant validation should be skipped for this request."""
        # Skip validation for health checks, static assets, and auth endpoints
        skip_paths = [
            '/health',
            '/static/',
            '/api/v1/security/csrf_token',
            '/api/v1/security/login',
            '/api/v1/security/refresh',
            '/login/',
            '/logout/',
            '/oauth-authorized/',
            '/metrics',
        ]
        
        path = request.path
        
        # Skip for specific paths
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return True
        
        # Skip for OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            return True
        
        return False
    
    def _create_error_response(self, message: str, status_code: int) -> Response:
        """Create a JSON error response."""
        response_data = {
            "error": message,
            "status_code": status_code
        }
        
        response = jsonify(response_data)
        response.status_code = status_code
        return response
    
    def handle_tenant_error(self, error: TenantNotFoundError) -> Response:
        """Handle tenant-related errors."""
        logger.error(f"Tenant error: {str(error)}")
        return self._create_error_response(str(error), 400)

def init_tenant_middleware(app: Flask) -> None:
    """Initialize tenant middleware for the Flask app."""
    logger.info("Initializing tenant middleware")
    TenantMiddleware(app)
