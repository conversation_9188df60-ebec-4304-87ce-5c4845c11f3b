# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

"""Multi-tenant utilities for Superset."""

import logging
import os
from typing import Dict, Optional, Any
from flask import request, g, current_app
from superset.exceptions import SupersetException

logger = logging.getLogger(__name__)

class TenantNotFoundError(SupersetException):
    """Raised when a tenant is not found or not configured."""
    pass

class TenantManager:
    """Manages tenant context and database configurations."""

    def __init__(self):
        self._tenant_configs: Dict[str, Dict[str, Any]] = {}
        self._localenv_config = self._load_localenv_config()

    def _load_localenv_config(self) -> Dict[str, str]:
        """Load configuration from .localenv file."""
        config = {}
        localenv_path = os.path.join(os.getcwd(), '.localenv')

        if os.path.exists(localenv_path):
            try:
                with open(localenv_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            config[key.strip()] = value.strip()
                logger.info(f"Loaded configuration from .localenv: {len(config)} variables")
            except Exception as e:
                logger.error(f"Error loading .localenv: {e}")
        else:
            logger.warning(f".localenv file not found at {localenv_path}")

        return config
    
    def get_current_tenant(self) -> Optional[str]:
        """Get the current tenant ID from route parameters."""
        if hasattr(g, 'tenant_id'):
            return g.tenant_id

        # Extract tenant from route parameters
        # Expected route format: /tenant/{tenant_id}/...
        tenant_id = self._extract_tenant_from_route()

        if tenant_id:
            # Validate tenant exists in configuration
            if not self.is_valid_tenant(tenant_id):
                raise TenantNotFoundError(f"Tenant '{tenant_id}' is not configured or does not exist")

            # Store in request context
            g.tenant_id = tenant_id
            return tenant_id

        return None

    def _extract_tenant_from_route(self) -> Optional[str]:
        """Extract tenant ID from route parameters."""
        try:
            # Check if tenant_id is in view args (URL parameters)
            if hasattr(request, 'view_args') and request.view_args:
                tenant_id = request.view_args.get('tenant_id')
                if tenant_id:
                    return tenant_id

            # Fallback: parse from URL path
            # Expected format: /tenant/{tenant_id}/...
            path_parts = request.path.strip('/').split('/')
            if len(path_parts) >= 2 and path_parts[0] == 'tenant':
                return path_parts[1]

            return None
        except Exception as e:
            logger.error(f"Error extracting tenant from route: {e}")
            return None
    
    def is_valid_tenant(self, tenant_id: str) -> bool:
        """Check if a tenant is valid and configured."""
        if not tenant_id:
            return False

        # Check if tenant is in the allowed list
        allowed_tenants = current_app.config.get('ALLOWED_TENANTS', [])
        if tenant_id not in allowed_tenants:
            return False

        # Optionally validate database exists (for production)
        if current_app.config.get('VALIDATE_TENANT_DB_EXISTS', False):
            return self._validate_database_exists(tenant_id)

        return True

    def _validate_database_exists(self, tenant_id: str) -> bool:
        """Validate that the tenant database actually exists."""
        try:
            from sqlalchemy import create_engine, text

            # Get database URI for tenant
            tenant_uri = self.get_tenant_database_uri(tenant_id)

            # Try to connect and verify database exists
            engine = create_engine(tenant_uri)
            with engine.connect() as conn:
                result = conn.execute(text("SELECT current_database()"))
                db_name = result.fetchone()[0]
                logger.info(f"Validated tenant database exists: {db_name}")
                return True

        except Exception as e:
            logger.error(f"Tenant database validation failed for {tenant_id}: {e}")
            return False
    
    def get_tenant_database_config(self, tenant_id: str) -> Dict[str, Any]:
        """Get database configuration for a specific tenant."""
        if not self.is_valid_tenant(tenant_id):
            raise TenantNotFoundError(f"Tenant '{tenant_id}' is not configured")
        
        # Check cache first
        if tenant_id in self._tenant_configs:
            return self._tenant_configs[tenant_id]
        
        # Get configuration from vault or environment
        config = self._get_tenant_config_from_vault(tenant_id)
        
        # Cache the configuration
        self._tenant_configs[tenant_id] = config
        
        return config
    
    def _get_tenant_config_from_vault(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant database configuration from .localenv file.

        Uses the database configuration from .localenv and creates tenant-specific database name.
        """
        # Get base configuration from .localenv
        base_config = {
            'host': self._localenv_config.get('DATABASE_HOST', 'localhost'),
            'port': int(self._localenv_config.get('DATABASE_PORT', 5432)),
            'dialect': self._localenv_config.get('DATABASE_DIALECT', 'postgresql'),
            'username': self._localenv_config.get('DATABASE_USER', 'postgres'),
            'password': self._localenv_config.get('DATABASE_PASSWORD', 'postgres'),
        }

        # Create tenant-specific database name
        # Use 'superset' as the suffix to match the initialization script
        tenant_database = f"{tenant_id}_superset"

        config = {
            **base_config,
            'database': tenant_database,
        }

        logger.info(f"Retrieved database config for tenant {tenant_id}: {tenant_database}")
        logger.debug(f"Database config: host={config['host']}, port={config['port']}, db={config['database']}")

        return config
    
    def get_tenant_database_uri(self, tenant_id: str) -> str:
        """Get the complete database URI for a tenant."""
        config = self.get_tenant_database_config(tenant_id)
        
        uri = (
            f"{config['dialect']}://"
            f"{config['username']}:{config['password']}@"
            f"{config['host']}:{config['port']}/{config['database']}"
        )
        
        return uri
    
    def clear_tenant_cache(self, tenant_id: Optional[str] = None):
        """Clear cached tenant configurations."""
        if tenant_id:
            self._tenant_configs.pop(tenant_id, None)
        else:
            self._tenant_configs.clear()

# Global tenant manager instance
tenant_manager = TenantManager()

def get_current_tenant() -> Optional[str]:
    """Convenience function to get current tenant ID."""
    return tenant_manager.get_current_tenant()

def require_tenant() -> str:
    """Get current tenant ID, raising an exception if none is found."""
    tenant_id = get_current_tenant()
    if not tenant_id:
        raise TenantNotFoundError("No tenant specified in request headers")
    return tenant_id

def get_tenant_database_uri(tenant_id: Optional[str] = None) -> str:
    """Get database URI for current or specified tenant."""
    if not tenant_id:
        tenant_id = require_tenant()
    return tenant_manager.get_tenant_database_uri(tenant_id)
