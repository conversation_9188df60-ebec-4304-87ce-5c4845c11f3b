# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

"""Multi-tenant utilities for Superset."""

import logging
from typing import Dict, Optional, Any
from flask import request, g, current_app
from superset.exceptions import SupersetException

logger = logging.getLogger(__name__)

class TenantNotFoundError(SupersetException):
    """Raised when a tenant is not found or not configured."""
    pass

class TenantManager:
    """Manages tenant context and database configurations."""
    
    TENANT_HEADER = "X-Tenant-ID"
    
    def __init__(self):
        self._tenant_configs: Dict[str, Dict[str, Any]] = {}
        self._vault_client = None  # Placeholder for vault integration
    
    def get_current_tenant(self) -> Optional[str]:
        """Get the current tenant ID from request context."""
        if hasattr(g, 'tenant_id'):
            return g.tenant_id
        
        tenant_id = request.headers.get(self.TENANT_HEADER)
        if tenant_id:
            # Validate tenant exists in configuration
            if not self.is_valid_tenant(tenant_id):
                raise TenantNotFoundError(f"Tenant '{tenant_id}' is not configured or does not exist")
            
            # Store in request context
            g.tenant_id = tenant_id
            return tenant_id
        
        return None
    
    def is_valid_tenant(self, tenant_id: str) -> bool:
        """Check if a tenant is valid and configured."""
        if not tenant_id:
            return False
        
        # Check if tenant is in the allowed list
        allowed_tenants = current_app.config.get('ALLOWED_TENANTS', [])
        return tenant_id in allowed_tenants
    
    def get_tenant_database_config(self, tenant_id: str) -> Dict[str, Any]:
        """Get database configuration for a specific tenant."""
        if not self.is_valid_tenant(tenant_id):
            raise TenantNotFoundError(f"Tenant '{tenant_id}' is not configured")
        
        # Check cache first
        if tenant_id in self._tenant_configs:
            return self._tenant_configs[tenant_id]
        
        # Get configuration from vault or environment
        config = self._get_tenant_config_from_vault(tenant_id)
        
        # Cache the configuration
        self._tenant_configs[tenant_id] = config
        
        return config
    
    def _get_tenant_config_from_vault(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant database configuration from vault.
        
        This is a placeholder implementation. Replace with actual vault integration.
        """
        # TODO: Implement actual vault integration
        # For now, return a configuration based on environment variables and tenant_id
        
        base_config = {
            'host': current_app.config.get('DATABASE_HOST', 'db'),
            'port': current_app.config.get('DATABASE_PORT', 5432),
            'dialect': current_app.config.get('DATABASE_DIALECT', 'postgresql'),
            'database': f"{tenant_id}_superset",
        }
        
        # In a real implementation, these would come from vault
        # vault_path = f"secret/tenants/{tenant_id}/database"
        # vault_data = self._vault_client.read(vault_path)
        
        # For now, use environment variables with tenant suffix
        # This allows you to set TENANT1_DB_USER, TENANT1_DB_PASSWORD, etc.
        env_prefix = f"{tenant_id.upper()}_DB_"
        
        config = {
            **base_config,
            'username': current_app.config.get(f'{env_prefix}USER', f'{tenant_id}_user'),
            'password': current_app.config.get(f'{env_prefix}PASSWORD', f'{tenant_id}_password'),
        }
        
        logger.info(f"Retrieved database config for tenant {tenant_id}: {tenant_id}_superset")
        
        return config
    
    def get_tenant_database_uri(self, tenant_id: str) -> str:
        """Get the complete database URI for a tenant."""
        config = self.get_tenant_database_config(tenant_id)
        
        uri = (
            f"{config['dialect']}://"
            f"{config['username']}:{config['password']}@"
            f"{config['host']}:{config['port']}/{config['database']}"
        )
        
        return uri
    
    def clear_tenant_cache(self, tenant_id: Optional[str] = None):
        """Clear cached tenant configurations."""
        if tenant_id:
            self._tenant_configs.pop(tenant_id, None)
        else:
            self._tenant_configs.clear()

# Global tenant manager instance
tenant_manager = TenantManager()

def get_current_tenant() -> Optional[str]:
    """Convenience function to get current tenant ID."""
    return tenant_manager.get_current_tenant()

def require_tenant() -> str:
    """Get current tenant ID, raising an exception if none is found."""
    tenant_id = get_current_tenant()
    if not tenant_id:
        raise TenantNotFoundError("No tenant specified in request headers")
    return tenant_id

def get_tenant_database_uri(tenant_id: Optional[str] = None) -> str:
    """Get database URI for current or specified tenant."""
    if not tenant_id:
        tenant_id = require_tenant()
    return tenant_manager.get_tenant_database_uri(tenant_id)
