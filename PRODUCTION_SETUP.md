# Production Multi-Tenant Setup Guide

This guide explains how to set up multi-tenant Superset in production where tenant databases already exist.

## Prerequisites

### 1. Tenant Databases Must Exist

Before deploying Superset, ensure all tenant databases are created:

```sql
-- Example: Create tenant databases
CREATE DATABASE tenant1_superset;
CREATE DATABASE tenant2_superset;
CREATE DATABASE tenant3_superset;

-- Grant permissions to Superset user
GRANT ALL PRIVILEGES ON DATABASE tenant1_superset TO superset_user;
GRANT ALL PRIVILEGES ON DATABASE tenant2_superset TO superset_user;
GRANT ALL PRIVILEGES ON DATABASE tenant3_superset TO superset_user;
```

### 2. Database Naming Convention

Tenant databases must follow the naming pattern: `{tenant_id}_superset`

Examples:
- Tenant ID: `acme` → Database: `acme_superset`
- Tenant ID: `corp` → Database: `corp_superset`
- Tenant ID: `startup` → Database: `startup_superset`

## Configuration

### 1. Environment Variables

Set these in your production environment:

```bash
# Enable multi-tenancy
MULTI_TENANT_ENABLED=true

# List of allowed tenants (comma-separated)
ALLOWED_TENANTS=tenant1,tenant2,tenant3,acme,corp

# Validate databases exist on startup (recommended for production)
VALIDATE_TENANT_DB_EXISTS=true
```

### 2. Database Credentials

Configure database connection in `.localenv` or environment:

```bash
DATABASE_HOST=your-postgres-host
DATABASE_PORT=5432
DATABASE_USER=superset_user
DATABASE_PASSWORD=your-secure-password
DATABASE_DIALECT=postgresql
```

## Deployment Steps

### 1. Pre-Deployment Database Setup

```sql
-- Connect to PostgreSQL as admin user
psql -h your-postgres-host -U postgres

-- Create tenant databases
CREATE DATABASE tenant1_superset;
CREATE DATABASE tenant2_superset;

-- Create or use existing Superset user
CREATE USER superset_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE tenant1_superset TO superset_user;
GRANT ALL PRIVILEGES ON DATABASE tenant2_superset TO superset_user;

-- Grant schema permissions
\c tenant1_superset
GRANT ALL ON SCHEMA public TO superset_user;

\c tenant2_superset
GRANT ALL ON SCHEMA public TO superset_user;
```

### 2. Initialize Superset Schema

Run Superset initialization for each tenant database:

```bash
# Set environment to point to tenant database
export DATABASE_DB=tenant1_superset
superset db upgrade

export DATABASE_DB=tenant2_superset
superset db upgrade
```

### 3. Deploy Application

Deploy Superset with multi-tenant configuration:

```bash
# Start Superset with multi-tenant enabled
docker-compose up -d
```

## Adding New Tenants

### 1. Create Database

```sql
-- Create new tenant database
CREATE DATABASE newtenant_superset;
GRANT ALL PRIVILEGES ON DATABASE newtenant_superset TO superset_user;

-- Initialize schema
\c newtenant_superset
GRANT ALL ON SCHEMA public TO superset_user;
```

### 2. Update Configuration

```bash
# Add to ALLOWED_TENANTS
ALLOWED_TENANTS=tenant1,tenant2,newtenant
```

### 3. Initialize Schema

```bash
export DATABASE_DB=newtenant_superset
superset db upgrade
```

### 4. Restart Application

```bash
docker-compose restart superset
```

## URL Structure

Tenants access their isolated Superset instance via:

```
https://your-superset-domain/tenant/{tenant_id}/dashboard/
https://your-superset-domain/tenant/{tenant_id}/explore/
https://your-superset-domain/tenant/{tenant_id}/sqllab/
```

Examples:
- `https://analytics.company.com/tenant/acme/dashboard/`
- `https://analytics.company.com/tenant/corp/explore/`

## Security Considerations

### 1. Database Isolation
- Each tenant has completely separate database
- No cross-tenant data access possible
- Database-level security enforced

### 2. Tenant Validation
- Only pre-configured tenants allowed
- Invalid tenant requests rejected with 400 error
- Database existence validated on startup (if enabled)

### 3. Connection Security
- Use SSL for database connections
- Secure credential management (vault recommended)
- Regular credential rotation

## Monitoring

### 1. Database Connections
Monitor database connections per tenant:

```sql
-- Check active connections per database
SELECT datname, count(*) 
FROM pg_stat_activity 
WHERE datname LIKE '%_superset' 
GROUP BY datname;
```

### 2. Application Logs
Monitor for tenant-related errors:

```bash
# Check for tenant validation errors
grep "Tenant.*not configured" /var/log/superset/superset.log

# Check database switching
grep "Switching to tenant database" /var/log/superset/superset.log
```

## Troubleshooting

### 1. Tenant Not Found Error
```
Error: Tenant 'xyz' is not configured or does not exist
```
**Solution**: Add tenant to `ALLOWED_TENANTS` and ensure database exists

### 2. Database Connection Error
```
Error: could not connect to server: FATAL: database "xyz_superset" does not exist
```
**Solution**: Create the tenant database and run schema initialization

### 3. Permission Denied
```
Error: permission denied for database "xyz_superset"
```
**Solution**: Grant proper permissions to Superset user

## Best Practices

1. **Database Naming**: Stick to `{tenant_id}_superset` convention
2. **Validation**: Enable `VALIDATE_TENANT_DB_EXISTS` in production
3. **Monitoring**: Set up alerts for tenant database connections
4. **Backup**: Include all tenant databases in backup strategy
5. **Security**: Use vault for credential management
6. **Documentation**: Maintain list of active tenants and their databases
