#!/usr/bin/env python3
"""
Validate that tenant databases exist and are accessible.
This script checks if the required tenant databases are available.
"""

import subprocess
import sys

def check_database_exists(database_name):
    """Check if a database exists using Docker exec."""
    try:
        result = subprocess.run([
            'docker', 'exec', 'superset_db',
            'psql', '-U', 'superset', '-lqt'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            databases = result.stdout
            return database_name in databases
        else:
            print(f"❌ Error checking databases: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Database check timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def validate_tenant_databases():
    """Validate all required tenant databases exist."""
    print("🔍 Validating Tenant Databases")
    print("=" * 50)
    
    # Expected tenant databases
    required_databases = [
        'postgres',           # Main database
        'superset',          # Superset metadata
        'tenant1_superset',  # Tenant 1
        'tenant2_superset',  # Tenant 2
    ]
    
    all_valid = True
    
    for db in required_databases:
        print(f"Checking {db}...", end=" ")
        
        if check_database_exists(db):
            print("✅ EXISTS")
        else:
            print("❌ MISSING")
            all_valid = False
    
    print("\n" + "=" * 50)
    
    if all_valid:
        print("🎉 All tenant databases are available!")
        print("\nYou can now:")
        print("1. Start Superset: docker-compose -f docker-compose-dev.yml up")
        print("2. Test tenant URLs:")
        print("   - http://localhost:8088/tenant/tenant1/dashboard/")
        print("   - http://localhost:8088/tenant/tenant2/explore/")
    else:
        print("❌ Some databases are missing!")
        print("\nTo create missing databases:")
        print("1. Connect to PostgreSQL:")
        print("   docker exec -it superset_db psql -U superset")
        print("2. Create missing databases:")
        for db in required_databases:
            if not check_database_exists(db) and db.endswith('_superset'):
                print(f"   CREATE DATABASE {db};")
    
    return all_valid

def create_missing_databases():
    """Create any missing tenant databases."""
    print("\n🔧 Creating Missing Tenant Databases")
    print("=" * 50)
    
    tenant_databases = ['tenant1_superset', 'tenant2_superset']
    
    for db in tenant_databases:
        if not check_database_exists(db):
            print(f"Creating {db}...")
            
            try:
                result = subprocess.run([
                    'docker', 'exec', 'superset_db',
                    'psql', '-U', 'superset', '-c', f'CREATE DATABASE {db};'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"✅ Created {db}")
                else:
                    print(f"❌ Failed to create {db}: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ Error creating {db}: {e}")

def main():
    print("Multi-Tenant Database Validator")
    print("=" * 50)
    
    # Check if Docker container is running
    try:
        result = subprocess.run([
            'docker', 'exec', 'superset_db', 'echo', 'test'
        ], capture_output=True, timeout=5)
        
        if result.returncode != 0:
            print("❌ PostgreSQL container 'superset_db' is not running")
            print("Start it with: docker-compose -f docker-compose-dev.yml up -d db")
            sys.exit(1)
            
    except Exception:
        print("❌ Cannot connect to PostgreSQL container")
        print("Make sure Docker is running and container exists")
        sys.exit(1)
    
    # Validate databases
    all_valid = validate_tenant_databases()
    
    if not all_valid:
        response = input("\nWould you like to create missing databases? (y/N): ")
        if response.lower() == 'y':
            create_missing_databases()
            print("\nRe-validating...")
            validate_tenant_databases()

if __name__ == "__main__":
    main()
