#!/usr/bin/env python3
"""
Test script for multi-tenant functionality.
This script tests the tenant identification and database switching functionality.
"""

import os
import sys
import requests
import json

# Add the superset directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'superset'))

def test_tenant_endpoints():
    """Test multi-tenant endpoints with different tenant headers."""
    
    base_url = "http://localhost:8088"
    
    # Test cases with different tenant headers
    test_cases = [
        {
            "name": "Valid Tenant 1",
            "headers": {"X-Tenant-ID": "tenant1"},
            "expected_status": 200
        },
        {
            "name": "Valid Tenant 2", 
            "headers": {"X-Tenant-ID": "tenant2"},
            "expected_status": 200
        },
        {
            "name": "Invalid Tenant",
            "headers": {"X-Tenant-ID": "invalid_tenant"},
            "expected_status": 400
        },
        {
            "name": "No Tenant Header",
            "headers": {},
            "expected_status": 400
        }
    ]
    
    # Test the health endpoint (should work without tenant)
    print("Testing health endpoint (should work without tenant)...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health endpoint status: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Health endpoint error: {e}")
    
    # Test tenant-aware endpoints
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        print(f"Headers: {test_case['headers']}")
        
        try:
            # Test a simple endpoint that requires tenant context
            response = requests.get(
                f"{base_url}/api/v1/security/csrf_token",
                headers=test_case['headers'],
                timeout=5
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Expected: {test_case['expected_status']}")
            
            if response.status_code == test_case['expected_status']:
                print("✅ Test PASSED")
            else:
                print("❌ Test FAILED")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            print("❌ Test FAILED - Connection error")

def test_tenant_manager():
    """Test the tenant manager functionality directly."""
    
    print("\n" + "="*50)
    print("Testing Tenant Manager Functionality")
    print("="*50)
    
    try:
        from superset.utils.tenant import tenant_manager, TenantNotFoundError
        from flask import Flask
        
        # Create a test Flask app
        app = Flask(__name__)
        app.config['ALLOWED_TENANTS'] = ['tenant1', 'tenant2']
        app.config['DATABASE_HOST'] = 'db'
        app.config['DATABASE_PORT'] = 5432
        app.config['DATABASE_DIALECT'] = 'postgresql'
        app.config['TENANT1_DB_USER'] = 'tenant1_user'
        app.config['TENANT1_DB_PASSWORD'] = 'tenant1_password'
        app.config['TENANT2_DB_USER'] = 'tenant2_user'
        app.config['TENANT2_DB_PASSWORD'] = 'tenant2_password'
        
        with app.app_context():
            # Test valid tenants
            for tenant in ['tenant1', 'tenant2']:
                print(f"\nTesting tenant: {tenant}")
                
                if tenant_manager.is_valid_tenant(tenant):
                    print(f"✅ {tenant} is valid")
                    
                    try:
                        config = tenant_manager.get_tenant_database_config(tenant)
                        print(f"✅ Database config retrieved: {config['database']}")
                        
                        uri = tenant_manager.get_tenant_database_uri(tenant)
                        print(f"✅ Database URI generated: {uri}")
                        
                    except Exception as e:
                        print(f"❌ Error getting config: {e}")
                else:
                    print(f"❌ {tenant} is not valid")
            
            # Test invalid tenant
            print(f"\nTesting invalid tenant: invalid_tenant")
            if not tenant_manager.is_valid_tenant('invalid_tenant'):
                print("✅ Invalid tenant correctly rejected")
            else:
                print("❌ Invalid tenant incorrectly accepted")
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the correct directory")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("Multi-Tenant Test Script")
    print("="*50)
    
    # Test tenant manager functionality
    test_tenant_manager()
    
    # Test HTTP endpoints (only if server is running)
    print("\n" + "="*50)
    print("Testing HTTP Endpoints")
    print("="*50)
    print("Note: Make sure Superset is running on localhost:8088")
    
    test_tenant_endpoints()
    
    print("\n" + "="*50)
    print("Test completed!")
    print("="*50)
