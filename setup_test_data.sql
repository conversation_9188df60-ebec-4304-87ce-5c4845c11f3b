-- SQL script to create test data for multi-tenant testing
-- Run this script in each tenant database via DBeaver

-- Create a sample customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    tenant_info VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create a sample orders table
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    product_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data for TENANT1 (run this in tenant1_superset database)
-- Uncomment the section below when running in tenant1_superset
/*
INSERT INTO customers (name, email, tenant_info) VALUES
('<PERSON>', '<EMAIL>', 'Tenant 1 Customer'),
('<PERSON>', '<EMAIL>', 'Tenant 1 Customer'),
('<PERSON>', '<EMAIL>', 'Tenant 1 Customer');

INSERT INTO orders (customer_id, product_name, amount) VALUES
(1, 'Laptop', 999.99),
(1, 'Mouse', 29.99),
(2, 'Keyboard', 79.99),
(3, 'Monitor', 299.99);
*/

-- Insert sample data for TENANT2 (run this in tenant2_superset database)
-- Uncomment the section below when running in tenant2_superset
/*
INSERT INTO customers (name, email, tenant_info) VALUES
('David Wilson', '<EMAIL>', 'Tenant 2 Customer'),
('Eva Brown', '<EMAIL>', 'Tenant 2 Customer'),
('Frank Miller', '<EMAIL>', 'Tenant 2 Customer');

INSERT INTO orders (customer_id, product_name, amount) VALUES
(1, 'Tablet', 499.99),
(1, 'Case', 39.99),
(2, 'Headphones', 149.99),
(3, 'Charger', 49.99);
*/

-- Query to verify data (run in each database)
SELECT 
    c.name,
    c.email,
    c.tenant_info,
    COUNT(o.id) as order_count,
    SUM(o.amount) as total_spent
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.name, c.email, c.tenant_info
ORDER BY c.name;
